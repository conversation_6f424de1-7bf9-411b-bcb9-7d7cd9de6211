using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Attributes;

namespace webApi.Controllers
{
    /// <summary>
    /// Controller for managing users in the task management system
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    [Authorize] // يتطلب المصادقة لجميع العمليات
    public class UsersController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public UsersController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Get all users
        /// </summary>
        /// <returns>List of all users</returns>
        /// <response code="200">Returns the list of users</response>
        [HttpGet]
        [SupervisorOrAbove] // يتطلب صلاحية مشرف أو أعلى
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<User>>> GetUsers()
        {
            return await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .Where(u => !u.IsDeleted)
                .OrderBy(u => u.Name)
                .ToListAsync();
        }

        /// <summary>
        /// Get a specific user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details</returns>
        /// <response code="200">Returns the user</response>
        /// <response code="404">If the user is not found</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<User>> GetUser(int id)
        {
            var user = await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .FirstOrDefaultAsync(u => u.Id == id && !u.IsDeleted);

            if (user == null)
            {
                return NotFound();
            }

            return user;
        }

        /// <summary>
        /// Update a user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="user">Updated user data</param>
        /// <returns>No content</returns>
        /// <response code="204">User updated successfully</response>
        /// <response code="400">Invalid request</response>
        /// <response code="404">User not found</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutUser(int id, User user)
        {
            if (id != user.Id)
            {
                return BadRequest();
            }

            _context.Entry(user).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// Create a new user
        /// </summary>
        /// <param name="user">User data</param>
        /// <returns>Created user</returns>
        /// <response code="201">User created successfully</response>
        /// <response code="400">Invalid request</response>
        [HttpPost]
        [ManagerOrAbove] // يتطلب صلاحية مدير أو أعلى
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<User>> PostUser(User user)
        {
            user.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            user.IsActive = true;
            user.IsDeleted = false;

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetUser", new { id = user.Id }, user);
        }

        /// <summary>
        /// Delete a user (soft delete)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>No content</returns>
        /// <response code="204">User deleted successfully</response>
        /// <response code="404">User not found</response>
        [HttpDelete("{id}")]
        [AdminOnly] // يتطلب صلاحية مدير عام فقط
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Soft delete instead of hard delete
            user.IsDeleted = true;
            user.IsActive = false;
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// Get users by department
        /// </summary>
        /// <param name="departmentId">Department ID</param>
        /// <returns>List of users in the department</returns>
        /// <response code="200">Returns the list of users</response>
        [HttpGet("department/{departmentId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<User>>> GetUsersByDepartment(int departmentId)
        {
            return await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .Where(u => u.DepartmentId == departmentId && !u.IsDeleted && u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
        }

        /// <summary>
        /// Get users by role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>List of users with the specified role</returns>
        /// <response code="200">Returns the list of users</response>
        [HttpGet("role/{roleId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<User>>> GetUsersByRole(int roleId)
        {
            return await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .Where(u => u.Role == roleId && !u.IsDeleted && u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
        }

        /// <summary>
        /// Search users by name or email
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of matching users</returns>
        /// <response code="200">Returns the list of matching users</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<User>>> SearchUsers([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetUsers();
            }

            return await _context.Users
                .Include(u => u.Department)
                .Include(u => u.RoleNavigation)
                .Where(u => (u.Name.Contains(searchTerm) ||
                           u.Email.Contains(searchTerm) ||
                           (u.Username != null && u.Username.Contains(searchTerm))) &&
                           !u.IsDeleted && u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
        }

        private bool UserExists(int id)
        {
            return _context.Users.Any(e => e.Id == id && !e.IsDeleted);
        }
    }
}
