{"ConnectionStrings": {"DefaultConnection": "Data Source=.\\sqlexpress;Initial Catalog=databasetasks;Integrated Security=True;Encrypt=False;Trust Server Certificate=True"}, "Jwt": {"SecretKey": "TasksManagementSystemSecretKey2024!@#$%^&*()_+", "Issuer": "TasksManagementAPI", "Audience": "TasksManagementClient", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}}